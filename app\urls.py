from django.urls import path, re_path
from app import views

urlpatterns = [
    path('', views.index),

    # 新的后台管理框架路由
    path('framework/', views.admin_framework, name='admin_framework'),

    # 框架子页面路由 - 支持多级路由
    re_path(r'^framework/page/(?P<route_path>[\w/-]+)$', views.framework_page_view, name='framework_page'),

    # Vue混合渲染测试路由
    path('vue-test/', views.vue_test_page, name='vue_test'),
    
    # 智能页面路由 - 支持HTML和Vue模板混合
    re_path(r'^smart/(?P<page_name>[\w-]+)$', views.smart_page_view, name='smart_page'),

    # 处理pages目录下的页面请求（原有功能保持）
    re_path(r'^pages/(?P<page_name>[\w-]+\.html)$', views.page_view),
    # 处理根目录下的html页面请求（原有功能保持）
    re_path(r'^(?P<page_name>[\w-]+\.html)$', views.page_view),
]
