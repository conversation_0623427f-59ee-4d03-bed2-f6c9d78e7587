<template>
  <div class="caihong-mall">
    <!-- 主容器 -->
    <div class="main-container">
      <!-- 顶部区域 -->
      <div class="header-section">
        <!-- 蓝天白云背景 -->
        <div class="sky-background">
          <div class="clouds">
            <div class="cloud cloud-1"></div>
            <div class="cloud cloud-2"></div>
            <div class="cloud cloud-3"></div>
          </div>
          <!-- 热气球 -->
          <div class="hot-air-balloon"></div>
          <!-- Logo和标题 -->
          <div class="logo-section">
            <div class="logo">
              <div class="penguin-logo"></div>
            </div>
            <h1 class="title">彩虹云商城</h1>
          </div>
        </div>

        <!-- 导航栏 -->
        <div class="nav-bar">
          <button class="nav-btn">
            <span class="nav-icon">📢</span>
            公告
          </button>
          <button class="nav-btn">
            <span class="nav-icon">👤</span>
            宝贝
          </button>
          <button class="nav-btn">
            <span class="nav-icon">🔐</span>
            登录
          </button>
        </div>
      </div>

      <!-- 功能栏 -->
      <div class="function-bar">
        <button class="func-btn">
          <span class="func-icon">📋</span>
          下单
        </button>
        <button class="func-btn">
          <span class="func-icon">🔍</span>
          查询
        </button>
        <button class="func-btn active">
          <span class="func-icon">🌐</span>
          分站
        </button>
        <button class="func-btn">
          <span class="func-icon">📱</span>
          更多
        </button>
      </div>

      <!-- 通知栏 -->
      <div class="notice-bar">
        <div class="notice-content">
          <span class="heart-icon">❤️</span>
          <span class="notice-text">【07月30号】最新业务通知</span>
          <span class="speed-tag">高速通道</span>
        </div>
      </div>

      <!-- 搜索区域 -->
      <div class="search-section">
        <div class="search-container">
          <input
            type="text"
            class="search-input"
            placeholder="搜索商品"
            v-model="searchQuery"
          />
          <button class="search-btn">🔍</button>
        </div>

        <div class="category-selector">
          <select v-model="selectedCategory" class="category-select">
            <option value="all">选择商品</option>
            <option value="digital">请选择商品</option>
          </select>
        </div>

        <div class="action-buttons">
          <button class="cart-btn">加入购物车</button>
          <button class="buy-btn">立即购买</button>
        </div>
      </div>

      <!-- 数据统计区域 -->
      <div class="stats-section">
        <div class="stats-header">
          <span class="stats-icon">📊</span>
          数据统计
        </div>

        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">5天</div>
            <div class="stat-icon">🛍️</div>
            <div class="stat-label">全部商品</div>
          </div>

          <div class="stat-item">
            <div class="stat-number">0元</div>
            <div class="stat-icon">💰</div>
            <div class="stat-label">交易总数</div>
          </div>

          <div class="stat-item">
            <div class="stat-number">0笔</div>
            <div class="stat-icon">✅</div>
            <div class="stat-label">订单总数</div>
          </div>

          <div class="stat-item">
            <div class="stat-number">0个</div>
            <div class="stat-icon">🔄</div>
            <div class="stat-label">代理分站</div>
          </div>

          <div class="stat-item">
            <div class="stat-number">0元</div>
            <div class="stat-icon">💳</div>
            <div class="stat-label">今日交易</div>
          </div>

          <div class="stat-item">
            <div class="stat-number">0笔</div>
            <div class="stat-icon">✅</div>
            <div class="stat-label">今日订单</div>
          </div>
        </div>
      </div>

      <!-- 底部版权 -->
      <div class="footer">
        <div class="footer-text">
          彩虹云商城 ❤️ 2025 | **************
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CaihongMall',
  data() {
    return {
      searchQuery: '',
      selectedCategory: 'all'
    }
  },
  methods: {
    handleSearch() {
      console.log('搜索:', this.searchQuery)
    },
    handleCategoryChange() {
      console.log('分类变更:', this.selectedCategory)
    }
  }
}
</script>

<style scoped>
/* 全局样式 */
.caihong-mall {
  min-height: 100vh;
  background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 50%, #a8d0f8 100%);
  font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.main-container {
  width: 100%;
  max-width: 420px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

/* 顶部区域样式 */
.header-section {
  position: relative;
}

.sky-background {
  background: linear-gradient(to bottom, #87ceeb 0%, #e0f6ff 50%, #ffffff 100%);
  height: 180px;
  position: relative;
  overflow: hidden;
}

/* 云朵动画 */
.clouds {
  position: absolute;
  width: 100%;
  height: 100%;
}

.cloud {
  position: absolute;
  background: white;
  border-radius: 50px;
  opacity: 0.8;
}

.cloud::before,
.cloud::after {
  content: '';
  position: absolute;
  background: white;
  border-radius: 50px;
}

.cloud-1 {
  width: 60px;
  height: 20px;
  top: 20px;
  left: 20px;
  animation: float 20s infinite linear;
}

.cloud-1::before {
  width: 30px;
  height: 30px;
  top: -15px;
  left: 10px;
}

.cloud-1::after {
  width: 40px;
  height: 25px;
  top: -10px;
  right: 10px;
}

.cloud-2 {
  width: 80px;
  height: 25px;
  top: 40px;
  right: 30px;
  animation: float 25s infinite linear reverse;
}

.cloud-2::before {
  width: 35px;
  height: 35px;
  top: -18px;
  left: 15px;
}

.cloud-2::after {
  width: 45px;
  height: 30px;
  top: -12px;
  right: 15px;
}

.cloud-3 {
  width: 50px;
  height: 18px;
  top: 70px;
  left: 50%;
  transform: translateX(-50%);
  animation: float 30s infinite linear;
}

.cloud-3::before {
  width: 25px;
  height: 25px;
  top: -12px;
  left: 8px;
}

.cloud-3::after {
  width: 30px;
  height: 20px;
  top: -8px;
  right: 8px;
}

@keyframes float {
  0% { transform: translateX(-100px); }
  100% { transform: translateX(calc(100vw + 100px)); }
}

/* 热气球 */
.hot-air-balloon {
  position: absolute;
  top: 15px;
  right: 20px;
  width: 30px;
  height: 40px;
  background: linear-gradient(45deg, #ff6b6b, #ffa500);
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  animation: balloon-float 6s ease-in-out infinite;
}

.hot-air-balloon::before {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 15px;
  background: #8b4513;
}

.hot-air-balloon::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 12px;
  height: 8px;
  background: #d2691e;
  border-radius: 2px;
}

@keyframes balloon-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Logo区域 */
.logo-section {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
}

.logo {
  margin-bottom: 10px;
}

.penguin-logo {
  width: 80px;
  height: 80px;
  background: radial-gradient(circle at 30% 30%, #ffffff, #f0f0f0);
  border-radius: 50%;
  margin: 0 auto;
  position: relative;
  border: 3px solid #4a90e2;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.penguin-logo::before {
  content: '🐧';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 40px;
}

.title {
  color: #4a90e2;
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: 2px;
}

/* 导航栏样式 */
.nav-bar {
  display: flex;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.nav-btn {
  flex: 1;
  padding: 12px 8px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.nav-btn:hover {
  background: #e9ecef;
  color: #4a90e2;
}

.nav-btn:not(:last-child) {
  border-right: 1px solid #e9ecef;
}

.nav-icon {
  font-size: 16px;
}

/* 功能栏样式 */
.function-bar {
  display: flex;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
}

.func-btn {
  flex: 1;
  padding: 12px 8px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.func-btn:hover {
  background: #f8f9fa;
  color: #4a90e2;
}

.func-btn.active {
  background: #4a90e2;
  color: white;
}

.func-btn:not(:last-child) {
  border-right: 1px solid #e9ecef;
}

.func-icon {
  font-size: 16px;
}

/* 通知栏样式 */
.notice-bar {
  background: #fff3cd;
  border-bottom: 1px solid #ffeaa7;
  padding: 8px 15px;
}

.notice-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #856404;
}

.heart-icon {
  color: #dc3545;
}

.notice-text {
  flex: 1;
}

.speed-tag {
  background: #dc3545;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

/* 搜索区域样式 */
.search-section {
  padding: 20px;
  background: white;
}

.search-container {
  display: flex;
  margin-bottom: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.search-input {
  flex: 1;
  padding: 12px 15px;
  border: none;
  outline: none;
  font-size: 14px;
}

.search-input::placeholder {
  color: #999;
}

.search-btn {
  padding: 12px 15px;
  border: none;
  background: #f8f9fa;
  color: #666;
  cursor: pointer;
  transition: background 0.3s ease;
}

.search-btn:hover {
  background: #e9ecef;
}

.category-selector {
  margin-bottom: 15px;
}

.category-select {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.cart-btn,
.buy-btn {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cart-btn {
  background: #28a745;
  color: white;
}

.cart-btn:hover {
  background: #218838;
  transform: translateY(-2px);
}

.buy-btn {
  background: #007bff;
  color: white;
}

.buy-btn:hover {
  background: #0056b3;
  transform: translateY(-2px);
}

/* 数据统计区域样式 */
.stats-section {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.stats-header {
  padding: 15px 20px;
  background: #e9ecef;
  font-size: 16px;
  font-weight: bold;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-icon {
  font-size: 18px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1px;
  background: #dee2e6;
  padding: 1px;
}

.stat-item {
  background: white;
  padding: 20px 10px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-item:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
}

.stat-number {
  font-size: 18px;
  font-weight: bold;
  color: #4a90e2;
  margin-bottom: 8px;
}

.stat-icon {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
}

.stat-label {
  font-size: 12px;
  color: #666;
  line-height: 1.2;
}

/* 底部版权样式 */
.footer {
  background: #f8f9fa;
  padding: 15px 20px;
  text-align: center;
  border-top: 1px solid #e9ecef;
}

.footer-text {
  font-size: 12px;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .caihong-mall {
    padding: 10px;
  }

  .main-container {
    max-width: 100%;
  }

  .title {
    font-size: 20px;
  }

  .penguin-logo {
    width: 60px;
    height: 60px;
  }

  .penguin-logo::before {
    font-size: 30px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .nav-btn,
  .func-btn {
    font-size: 12px;
    padding: 10px 6px;
  }

  .search-section {
    padding: 15px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.main-container {
  animation: fadeIn 0.8s ease-out;
}

/* 按钮点击效果 */
.nav-btn:active,
.func-btn:active,
.cart-btn:active,
.buy-btn:active {
  transform: scale(0.95);
}

/* 统计项目动画 */
.stat-item {
  animation: fadeIn 0.6s ease-out;
}

.stat-item:nth-child(1) { animation-delay: 0.1s; }
.stat-item:nth-child(2) { animation-delay: 0.2s; }
.stat-item:nth-child(3) { animation-delay: 0.3s; }
.stat-item:nth-child(4) { animation-delay: 0.4s; }
.stat-item:nth-child(5) { animation-delay: 0.5s; }
.stat-item:nth-child(6) { animation-delay: 0.6s; }

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>