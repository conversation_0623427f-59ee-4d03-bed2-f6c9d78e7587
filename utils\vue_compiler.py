"""
Vue模板编译器
负责将Vue单文件组件编译为可执行的HTML、CSS和JavaScript
"""
import os
import re
import json
import hashlib
import subprocess
import tempfile
import logging
from django.conf import settings

logger = logging.getLogger(__name__)


class VueCompiler:
    """Vue单文件组件编译器"""
    
    def __init__(self):
        self.vue_settings = getattr(settings, 'VUE_SETTINGS', {})
        self.compiler_settings = self.vue_settings.get('COMPILER', {})
        self.template_settings = self.vue_settings.get('TEMPLATES', {})
        
    def compile_vue_file(self, vue_file_path, context=None):
        """
        编译Vue单文件组件
        
        Args:
            vue_file_path: Vue文件的完整路径
            context: Django上下文数据
            
        Returns:
            dict: 编译结果，包含html、javascript、css
        """
        try:
            # 读取Vue文件内容
            vue_content = self._read_vue_file(vue_file_path)
            
            # 解析Vue单文件组件
            parsed_components = self._parse_vue_file(vue_content)
            
            # 编译各个部分
            compiled_result = {
                'html': self._compile_template(parsed_components.get('template', ''), context),
                'javascript': self._compile_script(parsed_components.get('script', ''), context),
                'css': self._compile_style(parsed_components.get('style', ''))
            }
            
            return compiled_result
            
        except Exception as e:
            logger.error(f"Vue文件编译失败: {vue_file_path} - {str(e)}")
            raise
    
    def _read_vue_file(self, vue_file_path):
        """读取Vue文件内容"""
        try:
            with open(vue_file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取Vue文件失败: {vue_file_path} - {str(e)}")
            raise
    
    def _parse_vue_file(self, vue_content):
        """
        解析Vue单文件组件，提取template、script、style部分
        
        Args:
            vue_content: Vue文件内容
            
        Returns:
            dict: 包含template、script、style的字典
        """
        result = {
            'template': '',
            'script': '',
            'style': ''
        }
        
        try:
            # 提取template部分
            template_match = re.search(r'<template[^>]*>(.*?)</template>', vue_content, re.DOTALL)
            if template_match:
                result['template'] = template_match.group(1).strip()
            
            # 提取script部分
            script_match = re.search(r'<script[^>]*>(.*?)</script>', vue_content, re.DOTALL)
            if script_match:
                result['script'] = script_match.group(1).strip()
            
            # 提取style部分
            style_match = re.search(r'<style[^>]*>(.*?)</style>', vue_content, re.DOTALL)
            if style_match:
                result['style'] = style_match.group(1).strip()
            
            return result
            
        except Exception as e:
            logger.error(f"Vue文件解析失败: {str(e)}")
            raise
    
    def _compile_template(self, template_content, context):
        """
        编译Vue模板部分
        
        Args:
            template_content: 模板内容
            context: Django上下文数据
            
        Returns:
            str: 编译后的HTML
        """
        if not template_content:
            return ''
        
        try:
            # 直接返回模板内容，不进行分隔符转换
            # Vue应用会使用自定义分隔符 [[ ]] 来避免与Django模板冲突
            compiled_html = template_content
            
            return compiled_html
            
        except Exception as e:
            logger.error(f"模板编译失败: {str(e)}")
            return template_content
    
    def _compile_script(self, script_content, context):
        """
        编译Vue脚本部分
        
        Args:
            script_content: 脚本内容
            context: Django上下文数据
            
        Returns:
            str: 编译后的JavaScript
        """
        if not script_content:
            return self._get_default_vue_app()
        
        try:
            # 处理Vue组件定义
            compiled_js = script_content
            
            # 如果脚本包含export default，转换为Vue应用
            if 'export default' in compiled_js:
                # 提取组件配置
                component_config = re.search(r'export\s+default\s+({.*})', compiled_js, re.DOTALL)
                if component_config:
                    config_content = component_config.group(1)
                    
                    # 创建Vue应用
                    vue_app_js = f"""
                    const {{ createApp }} = Vue;
                    
                    const componentConfig = {config_content};
                    
                    // 添加Django数据到组件
                    if (window.__DJANGO_CONTEXT__) {{
                        if (componentConfig.data) {{
                            const originalData = componentConfig.data;
                            componentConfig.data = function() {{
                                const data = originalData.call(this);
                                return {{
                                    ...data,
                                    djangoContext: window.__DJANGO_CONTEXT__
                                }};
                            }};
                        }} else {{
                            componentConfig.data = function() {{
                                return {{
                                    djangoContext: window.__DJANGO_CONTEXT__
                                }};
                            }};
                        }}
                    }}
                    
                    // 创建并挂载Vue应用
                    const app = createApp(componentConfig);
                    app.config.compilerOptions.delimiters = ['[[', ']]'];
                    app.mount('#vue-app');
                    """
                    
                    compiled_js = vue_app_js
            else:
                # 如果不是标准的Vue组件，包装为Vue应用
                compiled_js = f"""
                const {{ createApp }} = Vue;
                
                const app = createApp({{
                    data() {{
                        return {{
                            djangoContext: window.__DJANGO_CONTEXT__ || {{}}
                        }};
                    }},
                    mounted() {{
                        {compiled_js}
                    }}
                }});
                
                app.config.compilerOptions.delimiters = ['[[', ']]'];
                app.mount('#vue-app');
                """
            
            return compiled_js
            
        except Exception as e:
            logger.error(f"脚本编译失败: {str(e)}")
            return self._get_default_vue_app()
    
    def _compile_style(self, style_content):
        """
        编译Vue样式部分
        
        Args:
            style_content: 样式内容
            
        Returns:
            str: 编译后的CSS
        """
        if not style_content:
            return ''
        
        try:
            # 处理scoped样式（简单实现）
            compiled_css = style_content
            
            # 这里可以添加CSS预处理器支持（如Sass、Less）
            # 目前只是简单返回原始CSS
            
            return compiled_css
            
        except Exception as e:
            logger.error(f"样式编译失败: {str(e)}")
            return style_content
    
    def _get_default_vue_app(self):
        """获取默认的Vue应用代码"""
        return """
        const { createApp } = Vue;
        
        const app = createApp({
            data() {
                return {
                    djangoContext: window.__DJANGO_CONTEXT__ || {}
                };
            }
        });
        
        app.config.compilerOptions.delimiters = ['[[', ']]'];
        app.mount('#vue-app');
        """
    
    def _get_cache_key(self, vue_file_path):
        """生成缓存键"""
        file_stat = os.stat(vue_file_path)
        content = f"{vue_file_path}_{file_stat.st_mtime}_{file_stat.st_size}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def compile_with_node(self, vue_file_path):
        """
        使用Node.js编译Vue文件（高级功能，需要Node.js环境）
        
        Args:
            vue_file_path: Vue文件路径
            
        Returns:
            dict: 编译结果
        """
        try:
            node_executable = self.compiler_settings.get('NODE_EXECUTABLE', 'node')
            compile_timeout = self.compiler_settings.get('COMPILE_TIMEOUT', 30)
            
            # 创建临时编译脚本
            compile_script = self._create_node_compile_script()
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as f:
                f.write(compile_script)
                script_path = f.name
            
            try:
                # 执行Node.js编译
                result = subprocess.run(
                    [node_executable, script_path, vue_file_path],
                    capture_output=True,
                    text=True,
                    timeout=compile_timeout
                )
                
                if result.returncode == 0:
                    return json.loads(result.stdout)
                else:
                    logger.error(f"Node.js编译失败: {result.stderr}")
                    raise Exception(f"Node.js编译失败: {result.stderr}")
                    
            finally:
                # 清理临时文件
                os.unlink(script_path)
                
        except Exception as e:
            logger.error(f"Node.js编译异常: {str(e)}")
            raise
    
    def _create_node_compile_script(self):
        """创建Node.js编译脚本"""
        return """
        const fs = require('fs');
        const path = require('path');
        
        // 简单的Vue文件解析器
        function parseVueFile(content) {
            const templateMatch = content.match(/<template[^>]*>([\\s\\S]*?)<\\/template>/);
            const scriptMatch = content.match(/<script[^>]*>([\\s\\S]*?)<\\/script>/);
            const styleMatch = content.match(/<style[^>]*>([\\s\\S]*?)<\\/style>/);
            
            return {
                template: templateMatch ? templateMatch[1].trim() : '',
                script: scriptMatch ? scriptMatch[1].trim() : '',
                style: styleMatch ? styleMatch[1].trim() : ''
            };
        }
        
        // 主编译函数
        function compileVueFile(filePath) {
            const content = fs.readFileSync(filePath, 'utf-8');
            const parsed = parseVueFile(content);
            
            return {
                html: parsed.template,
                javascript: parsed.script,
                css: parsed.style
            };
        }
        
        // 命令行参数处理
        const filePath = process.argv[2];
        if (!filePath) {
            console.error('请提供Vue文件路径');
            process.exit(1);
        }
        
        try {
            const result = compileVueFile(filePath);
            console.log(JSON.stringify(result));
        } catch (error) {
            console.error('编译失败:', error.message);
            process.exit(1);
        }
        """