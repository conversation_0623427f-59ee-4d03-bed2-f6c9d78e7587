import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import fs from 'fs'

// 扫描Vue文件的辅助函数
function scanVueFiles(dir) {
  const entries = {}

  function scanDirectory(currentDir, relativePath = '') {
    if (!fs.existsSync(currentDir)) return

    const files = fs.readdirSync(currentDir)

    files.forEach(file => {
      const fullPath = path.join(currentDir, file)
      const stat = fs.statSync(fullPath)

      if (stat.isDirectory()) {
        scanDirectory(fullPath, path.join(relativePath, file))
      } else if (file.endsWith('.vue')) {
        const name = path.join(relativePath, file.replace('.vue', ''))
        const key = name.replace(/\\/g, '/') // 统一使用正斜杠
        entries[key] = fullPath
      }
    })
  }

  scanDirectory(dir)
  return entries
}

export default defineConfig({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          // 使用自定义分隔符避免与Django模板语法冲突
          delimiters: ['[[', ']]']
        }
      }
    })
  ],

  root: path.resolve(__dirname, 'src'),

  build: {
    // 构建输出到Django模板目录
    outDir: path.resolve(__dirname, '../templates/vue'),
    emptyOutDir: false, // 不清空目录，保持现有HTML模板

    rollupOptions: {
      input: {
        // 动态扫描src目录下的Vue文件
        ...scanVueFiles(path.resolve(__dirname, 'src'))
      },
      output: {
        // 保持目录结构
        entryFileNames: '[name].js',
        chunkFileNames: 'chunks/[name].js',
        assetFileNames: 'assets/[name].[ext]',

        // 确保输出文件保持相对路径结构
        manualChunks: undefined
      }
    },

    // 生成manifest文件用于Django引用
    manifest: true,

    // 优化配置
    minify: 'terser',
    sourcemap: false,

    // 确保Vue组件能正确构建
    lib: false
  },

  server: {
    port: 5173,
    host: 'localhost',

    // 代理Django服务器
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/static': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/media': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/admin': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      }
    },

    // 热更新配置
    hmr: {
      port: 5174,
      host: 'localhost'
    },

    // 开发服务器配置
    cors: true,
    open: false // 不自动打开浏览器
  },

  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      'vue': 'vue/dist/vue.esm-bundler.js'
    }
  },

  css: {
    preprocessorOptions: {
      scss: {
        // 移除全局样式变量导入，每个组件独立管理样式
      }
    }
  },

  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false
  }
})