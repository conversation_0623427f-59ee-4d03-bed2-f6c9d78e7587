{"name": "django-vue-hybrid-frontend", "version": "1.0.0", "description": "Vue.js frontend for Django hybrid rendering system", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build:watch": "vite build --watch", "serve": "concurrently \"npm run dev\" \"cd .. && python manage.py runserver\""}, "dependencies": {"vue": "^3.4.0", "@vue/runtime-dom": "^3.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "vite": "^5.0.0", "concurrently": "^8.2.0", "@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "sass": "^1.69.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}