#!/usr/bin/env python3
"""
Django 项目部署配置脚本
支持开发环境和生产环境的快速切换
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import yaml

class DeploymentManager:
    def __init__(self):
        self.base_dir = Path(__file__).resolve().parent
        self.env_file = self.base_dir / '.env'
        self.config_file = self.base_dir / 'deploy_config.yaml'

    def create_config_template(self):
        """创建YAML配置文件模板"""
        config_template = {
            'database': {
                'engine': 'django.db.backends.mysql',
                'name': '',  # 数据库名称
                'user': '',  # 数据库用户名
                'password': '',  # 数据库密码
                'host': 'localhost',
                'port': '3306'
            },
            'email': {
                'host': '',  # SMTP服务器，如: smtp.qq.com
                'port': 465,
                'use_ssl': True,
                'user': '',  # 发送邮件的邮箱账号
                'password': ''  # 邮箱授权码
            },
            'admin': {
                'username': '',  # 后台管理员用户名
                'password': ''   # 后台管理员密码
            },
            'server': {
                'ip': '',      # 服务器IP地址
                'domain': '',  # 域名（可选）
                'port': 8000   # 服务端口
            }
        }

        if not self.config_file.exists():
            try:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    f.write("# Django 项目部署配置文件\n")
                    f.write("# 请根据实际情况填写以下配置信息\n")
                    f.write("# 注意: 此文件包含敏感信息，请不要提交到Git仓库\n\n")

                    # 手动格式化YAML以添加注释
                    f.write("# 数据库配置\n")
                    f.write("database:\n")
                    f.write("  engine: django.db.backends.mysql\n")
                    f.write("  name: ''          # 数据库名称\n")
                    f.write("  user: ''          # 数据库用户名\n")
                    f.write("  password: ''      # 数据库密码\n")
                    f.write("  host: localhost\n")
                    f.write("  port: '3306'\n\n")

                    f.write("# 邮件配置\n")
                    f.write("email:\n")
                    f.write("  host: ''          # SMTP服务器，如: smtp.qq.com\n")
                    f.write("  port: 465\n")
                    f.write("  use_ssl: true\n")
                    f.write("  user: ''          # 发送邮件的邮箱账号\n")
                    f.write("  password: ''      # 邮箱授权码\n\n")

                    f.write("# 管理员配置\n")
                    f.write("admin:\n")
                    f.write("  username: ''      # 后台管理员用户名\n")
                    f.write("  password: ''      # 后台管理员密码\n\n")

                    f.write("# 服务器配置\n")
                    f.write("server:\n")
                    f.write("  ip: ''            # 服务器IP地址\n")
                    f.write("  domain: ''        # 域名（可选）\n")
                    f.write("  port: 8000        # 服务端口\n")

                print(f"✅ 已创建配置文件模板: {self.config_file}")
                print("📝 请编辑此文件并填写实际的配置信息")
            except Exception as e:
                print(f"❌ 创建配置文件失败: {e}")
        else:
            print(f"📋 配置文件已存在: {self.config_file}")

    def load_config(self, debug=False):
        """
        加载YAML配置文件 - 改进版解析器

        Args:
            debug (bool): 是否输出调试信息

        Returns:
            dict: 解析后的配置字典，失败时返回None
        """
        if not self.config_file.exists():
            if debug:
                print(f"❌ 配置文件不存在: {self.config_file}")
            return None

        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            config = {}
            current_section = None
            line_number = 0

            if debug:
                print(f"📖 开始解析配置文件: {self.config_file}")
                print(f"📄 文件总行数: {len(lines)}")

            for line in lines:
                line_number += 1
                original_line = line
                line = line.rstrip('\n\r')  # 只移除行尾换行符

                # 跳过空行
                if not line.strip():
                    continue

                # 跳过纯注释行
                if line.strip().startswith('#'):
                    continue

                # 检查是否包含冒号
                if ':' not in line:
                    continue

                # 分离注释（处理行内注释）
                comment_pos = line.find('#')
                if comment_pos != -1:
                    # 检查#是否在引号内
                    before_comment = line[:comment_pos]
                    single_quotes = before_comment.count("'")
                    double_quotes = before_comment.count('"')

                    # 如果引号成对出现，说明#在引号外，是注释
                    if single_quotes % 2 == 0 and double_quotes % 2 == 0:
                        line = line[:comment_pos]

                line = line.rstrip()  # 移除右侧空白

                if not line:
                    continue

                # 判断是否为顶级键（不以空格或制表符开头）
                if not line.startswith((' ', '\t')):
                    # 顶级键
                    if ':' in line:
                        key_part = line.split(':', 1)[0].strip()
                        current_section = key_part
                        config[current_section] = {}

                        if debug:
                            print(f"🔍 第{line_number}行: 发现顶级键 '{current_section}'")
                else:
                    # 子键
                    if current_section is None:
                        if debug:
                            print(f"⚠️  第{line_number}行: 发现子键但没有顶级键，跳过")
                        continue

                    # 分割键值对
                    key_value_parts = line.split(':', 1)
                    if len(key_value_parts) != 2:
                        if debug:
                            print(f"⚠️  第{line_number}行: 无效的键值对格式，跳过")
                        continue

                    key = key_value_parts[0].strip()
                    value_part = key_value_parts[1].strip()

                    # 解析值
                    parsed_value = self._parse_yaml_value(value_part)

                    # 存储到配置中
                    if current_section not in config:
                        config[current_section] = {}

                    config[current_section][key] = parsed_value

                    if debug:
                        print(f"🔍 第{line_number}行: {current_section}.{key} = '{parsed_value}' (类型: {type(parsed_value).__name__})")

            if debug:
                print(f"✅ 配置文件解析完成")
                print(f"📊 解析结果概览:")
                for section, items in config.items():
                    print(f"   [{section}]: {len(items)} 个配置项")
                    for key, value in items.items():
                        print(f"     {key}: '{value}'")

            return config

        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
            if debug:
                import traceback
                print(f"详细错误信息:\n{traceback.format_exc()}")
            return None

    def _parse_yaml_value(self, value_str):
        """
        解析YAML值，正确处理各种格式

        Args:
            value_str (str): 原始值字符串

        Returns:
            str: 解析后的值
        """
        if not value_str:
            return ''

        value_str = value_str.strip()

        # 处理空字符串
        if not value_str:
            return ''

        # 处理布尔值
        if value_str.lower() in ('true', 'yes', 'on'):
            return 'True'
        elif value_str.lower() in ('false', 'no', 'off'):
            return 'False'

        # 处理数字（保持为字符串，因为配置通常需要字符串格式）
        if value_str.isdigit():
            return value_str

        # 处理引号包围的字符串
        if len(value_str) >= 2:
            # 单引号字符串
            if value_str.startswith("'") and value_str.endswith("'"):
                return value_str[1:-1]  # 移除首尾单引号
            # 双引号字符串
            elif value_str.startswith('"') and value_str.endswith('"'):
                return value_str[1:-1]  # 移除首尾双引号

        # 普通字符串，直接返回
        return value_str

    def validate_config(self, config, debug=False):
        """
        验证配置文件是否完整 - 改进版

        Args:
            config (dict): 配置字典
            debug (bool): 是否输出调试信息

        Returns:
            tuple: (is_valid, message)
        """
        if not config:
            return False, "配置文件不存在或格式错误"

        required_fields = {
            'database': ['name', 'user', 'password'],
            'email': ['host', 'user', 'password'],
            'admin': ['username', 'password'],
            'server': ['ip']  # 服务器IP为必填项，域名可选
        }

        missing_fields = []
        empty_fields = []

        if debug:
            print(f"🔍 开始验证配置...")
            print(f"📋 需要验证的配置节: {list(required_fields.keys())}")

        for section, fields in required_fields.items():
            if section not in config:
                missing_fields.append(f"缺少 {section} 配置节")
                if debug:
                    print(f"❌ 缺少配置节: {section}")
                continue

            if debug:
                print(f"✅ 找到配置节: {section}")

            for field in fields:
                if field not in config[section]:
                    missing_fields.append(f"{section}.{field} (字段不存在)")
                    if debug:
                        print(f"❌ {section}.{field}: 字段不存在")
                elif not config[section][field]:
                    empty_fields.append(f"{section}.{field}")
                    if debug:
                        print(f"❌ {section}.{field}: 值为空 ('{config[section][field]}')")
                else:
                    if debug:
                        print(f"✅ {section}.{field}: '{config[section][field]}'")

        # 合并错误信息
        all_errors = missing_fields + empty_fields

        if all_errors:
            error_msg = f"配置验证失败:\n"
            if missing_fields:
                error_msg += f"  缺少字段: {', '.join(missing_fields)}\n"
            if empty_fields:
                error_msg += f"  空值字段: {', '.join(empty_fields)}"
            return False, error_msg.strip()

        if debug:
            print(f"✅ 配置验证通过")

        return True, "配置验证通过"

    def reset_config_for_preproduction(self):
        """重置YAML配置文件为空（预生产环境专用）"""
        empty_config = """# Django 项目部署配置文件
# 预生产环境 - 配置已重置为空，确保Git提交时不包含敏感信息
# 请在生产环境部署前填写实际配置信息

# 数据库配置
database:
  engine: django.db.backends.mysql
  name: ''          # 数据库名称
  user: ''          # 数据库用户名
  password: ''      # 数据库密码
  host: localhost
  port: '3306'

# 邮件配置
email:
  host: ''          # SMTP服务器，如: smtp.qq.com
  port: 465
  use_ssl: true
  user: ''          # 发送邮件的邮箱账号
  password: ''      # 邮箱授权码

# 管理员配置
admin:
  username: ''      # 后台管理员用户名
  password: ''      # 后台管理员密码

# 服务器配置
server:
  ip: ''            # 服务器IP地址
  domain: ''        # 域名（可选）
  port: 8000        # 服务端口
"""

        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                f.write(empty_config)
            print(f"✅ 已重置配置文件为空: {self.config_file}")
            print("🔒 确保Git提交时不包含敏感信息")
        except Exception as e:
            print(f"❌ 重置配置文件失败: {e}")

    def create_production_env(self, debug=False):
        """
        创建生产环境配置 - 改进版

        Args:
            debug (bool): 是否启用调试模式
        """
        print("🚀 配置生产环境...")

        # 确保配置文件存在
        self.create_config_template()

        # 加载配置文件（启用调试）
        print("📖 正在读取配置文件...")
        config = self.load_config(debug=debug)

        if config is None:
            print("❌ 无法读取配置文件!")
            return False

        # 验证配置文件（启用调试）
        print("🔍 正在验证配置...")
        is_valid, message = self.validate_config(config, debug=debug)

        if not is_valid:
            print("❌ 配置验证失败!")
            print(f"{message}")
            print("")
            print("🔧 故障排除建议:")
            print("1. 检查配置文件格式是否正确")
            print("2. 确保所有必填字段都有值")
            print("3. 检查引号和缩进是否正确")
            print("")
            print("📝 请编辑配置文件并填写完整信息:")
            print(f"   {self.config_file}")
            print("")
            print("💡 正确的配置文件格式示例:")
            print("   database:")
            print("     name: 'shopping_db'")
            print("     user: 'shopping_db'")
            print("     password: 'your_password'")
            print("   email:")
            print("     host: 'smtp.qq.com'")
            print("     user: '<EMAIL>'")
            print("     password: 'your_email_auth_code'")
            print("   admin:")
            print("     username: 'admin'")
            print("     password: 'secure_password'")
            print("   server:")
            print("     ip: '*************'")
            print("     domain: 'example.com'  # 可选")
            print("")
            print("🐛 如需详细调试信息，请运行:")
            print("   python deploy.py production --debug")
            return False

        print("✅ 配置验证通过")

        # 确保config不为None（验证通过后应该不会为None）
        assert config is not None, "配置验证通过但config为None"

        # 生成安全的 SECRET_KEY
        import secrets
        import string
        secret_key = ''.join(secrets.choice(string.ascii_letters + string.digits + '!@#$%^&*') for _ in range(50))

        # 生成 JWT SECRET_KEY
        jwt_secret = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(64))

        # 构建 ALLOWED_HOSTS
        allowed_hosts = ['localhost', '127.0.0.1']

        # 从配置文件读取服务器信息
        server_config = config.get('server', {})
        server_ip = server_config.get('ip', '')
        domain = server_config.get('domain', '')

        if server_ip:
            allowed_hosts.append(server_ip)
        if domain:
            allowed_hosts.extend([domain, f'www.{domain}'])

        # 动态获取项目路径
        project_root = self.base_dir
        static_root = project_root / 'staticfiles'
        media_root = project_root / 'media'
        
        production_config = f"""# Django 生产环境配置

# 环境设置
DJANGO_ENV=production

# Django 基础配置
DEBUG=False
SECRET_KEY={secret_key}

# 允许访问的主机列表
ALLOWED_HOSTS={','.join(allowed_hosts)}

# 数据库配置
DB_ENGINE={config['database']['engine']}
DB_NAME={config['database']['name']}
DB_USER={config['database']['user']}
DB_PASSWORD={config['database']['password']}
DB_HOST={config['database']['host']}
DB_PORT={config['database']['port']}

# JWT 配置
JWT_SECRET_KEY={jwt_secret}

# 邮件配置
EMAIL_HOST={config['email']['host']}
EMAIL_PORT={config['email']['port']}
EMAIL_USE_SSL={str(config['email']['use_ssl']).title()}
EMAIL_HOST_USER={config['email']['user']}
EMAIL_HOST_PASSWORD={config['email']['password']}

# 静态文件配置
STATIC_URL=/static/
STATIC_ROOT={static_root}

# 媒体文件配置
MEDIA_URL=/media/
MEDIA_ROOT={media_root}

# 管理员配置
ADMIN_USERNAME={config['admin']['username']}
ADMIN_PASSWORD={config['admin']['password']}

# 服务器配置
SERVER_PORT=8000
SERVER_IP={server_ip}
DOMAIN={domain}
"""
        
        # 备份现有配置
        if self.env_file.exists():
            backup_file = self.base_dir / '.env.backup'
            shutil.copy2(self.env_file, backup_file)
            print(f"✅ 已备份现有配置到: {backup_file}")
        
        # 写入生产环境配置
        with open(self.env_file, 'w', encoding='utf-8') as f:
            f.write(production_config)
        
        print("✅ 生产环境配置已创建")
        print(f"   服务器IP: {server_ip or '未设置'}")
        print(f"   域名: {domain or '未设置'}")
        print(f"   允许主机: {allowed_hosts}")
        return True
        
    def create_development_env(self):
        """创建开发环境配置（通用版本，不包含私密信息）"""
        print("🔧 配置开发环境...")

        # 检查是否存在本地开发配置文件
        dev_config_file = self.base_dir / 'dev_config.py'
        if dev_config_file.exists():
            print("⚠️  检测到本地开发配置文件 dev_config.py")
            print("💡 建议使用本地配置文件以获得完整的开发环境配置:")
            print("   python dev_config.py")
            print("")
            print("继续使用通用开发环境配置？")
            print("  输入 'y' 或 'yes' 继续使用通用配置")
            print("  输入其他任意键退出，使用本地配置")

            try:
                user_input = input("请选择: ").strip().lower()
                if user_input not in ['y', 'yes', '是', '继续']:
                    print("已取消，请运行: python dev_config.py")
                    return False
            except KeyboardInterrupt:
                print("\n已取消")
                return False

        # 动态获取项目路径
        project_root = self.base_dir
        static_root = project_root / 'staticfiles'
        media_root = project_root / 'media'

        development_config = f"""# Django 开发环境配置（通用版本）
# 注意: 此配置不包含私密信息，仅供参考

# 环境设置
DJANGO_ENV=development

# Django 基础配置
DEBUG=True
SECRET_KEY=django-insecure-please-change-this-in-production

# 允许访问的主机列表（开发环境）
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库配置（请根据实际情况修改）
DB_ENGINE=django.db.backends.mysql
DB_NAME=your_database_name
DB_USER=your_username
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306

# JWT 配置
JWT_SECRET_KEY=

# 邮件配置（请根据实际情况修改）
EMAIL_HOST=smtp.example.com
EMAIL_PORT=465
EMAIL_USE_SSL=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password

# 静态文件配置
STATIC_URL=/static/
STATIC_ROOT={static_root}

# 媒体文件配置
MEDIA_URL=/media/
MEDIA_ROOT={media_root}

# 管理员配置（请根据实际情况修改）
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin

# 服务器配置
SERVER_PORT=8000
"""

        # 备份现有配置
        if self.env_file.exists():
            backup_file = self.base_dir / '.env.backup'
            shutil.copy2(self.env_file, backup_file)
            print(f"✅ 已备份现有配置到: {backup_file}")

        # 写入开发环境配置
        with open(self.env_file, 'w', encoding='utf-8') as f:
            f.write(development_config)

        print("✅ 通用开发环境配置已创建")
        print("⚠️  注意: 此配置不包含实际的数据库和邮件信息")
        print("💡 如需完整配置，请编辑 .env 文件或使用 dev_config.py")
        return True

    def create_preproduction_env(self):
        """创建预生产环境配置"""
        print("🧪 配置预生产环境...")

        # 重置YAML配置文件为空（确保Git提交时不包含敏感信息）
        self.reset_config_for_preproduction()

        # 确保配置文件存在
        self.create_config_template()

        # 动态获取项目路径
        project_root = self.base_dir
        static_root = project_root / 'staticfiles'
        media_root = project_root / 'media'

        # 生成安全的 SECRET_KEY
        import secrets
        import string
        secret_key = ''.join(secrets.choice(string.ascii_letters + string.digits + '!@#$%^&*') for _ in range(50))

        # 生成 JWT SECRET_KEY
        jwt_secret = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(64))

        preproduction_config = f"""# Django 预生产环境配置
# 用于上传Git仓库前的测试环境

# 环境设置
DJANGO_ENV=preproduction

# Django 基础配置
DEBUG=False
SECRET_KEY={secret_key}

# 允许访问的主机列表（预生产环境 - 待确定服务器信息）
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库配置（预生产环境 - 配置为空）
DB_ENGINE=django.db.backends.mysql
DB_NAME=
DB_USER=
DB_PASSWORD=
DB_HOST=localhost
DB_PORT=3306

# JWT 配置
JWT_SECRET_KEY={jwt_secret}

# 邮件配置（预生产环境 - 配置为空）
EMAIL_HOST=
EMAIL_PORT=465
EMAIL_USE_SSL=True
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=

# 静态文件配置
STATIC_URL=/static/
STATIC_ROOT={static_root}

# 媒体文件配置
MEDIA_URL=/media/
MEDIA_ROOT={media_root}

# 管理员配置（预生产环境 - 配置为空）
ADMIN_USERNAME=
ADMIN_PASSWORD=

# 服务器配置
SERVER_PORT=8000
"""

        # 备份现有配置
        if self.env_file.exists():
            backup_file = self.base_dir / '.env.backup'
            shutil.copy2(self.env_file, backup_file)
            print(f"✅ 已备份现有配置到: {backup_file}")

        # 写入预生产环境配置
        with open(self.env_file, 'w', encoding='utf-8') as f:
            f.write(preproduction_config)

        print("✅ 预生产环境配置已创建")
        print("⚠️  注意: 这是预生产环境，部署前请切换到正式生产环境")

    def build_frontend(self):
        """构建前端资源"""
        print("🔨 构建前端资源...")
        
        frontend_dir = self.base_dir / 'frontend'
        if not frontend_dir.exists():
            print("⚠️  未找到frontend目录，跳过前端构建")
            return True
            
        package_json = frontend_dir / 'package.json'
        if not package_json.exists():
            print("⚠️  未找到package.json，跳过前端构建")
            return True
        
        try:
            # 检查Node.js和npm是否可用
            print("🔍 检查Node.js环境...")
            node_result = subprocess.run(['node', '--version'], 
                                       capture_output=True, text=True)
            npm_result = subprocess.run(['npm', '--version'], 
                                      capture_output=True, text=True)
            
            if node_result.returncode != 0 or npm_result.returncode != 0:
                print("❌ Node.js或npm未安装，无法构建前端")
                print("💡 请先安装Node.js: https://nodejs.org/")
                return False
                
            print(f"✅ Node.js版本: {node_result.stdout.strip()}")
            print(f"✅ npm版本: {npm_result.stdout.strip()}")
            
            # 安装依赖
            print("📦 安装前端依赖...")
            install_result = subprocess.run([
                'npm', 'install'
            ], capture_output=True, text=True, cwd=frontend_dir)
            
            if install_result.returncode != 0:
                print(f"❌ 依赖安装失败: {install_result.stderr}")
                return False
            
            print("✅ 前端依赖安装完成")
            
            # 构建生产版本
            print("🏗️  构建生产版本...")
            build_result = subprocess.run([
                'npm', 'run', 'build'
            ], capture_output=True, text=True, cwd=frontend_dir)
            
            if build_result.returncode != 0:
                print(f"❌ 前端构建失败: {build_result.stderr}")
                return False
                
            print("✅ 前端构建完成")
            
            # 验证构建输出
            vue_templates_dir = self.base_dir / 'templates' / 'vue'
            if vue_templates_dir.exists():
                print(f"✅ 构建文件已输出到: {vue_templates_dir}")
            else:
                print("⚠️  构建完成但未找到输出目录")
                
            return True
            
        except FileNotFoundError:
            print("❌ npm命令未找到，请确保Node.js已正确安装")
            return False
        except Exception as e:
            print(f"❌ 前端构建出错: {e}")
            return False

    def collect_static_files(self):
        """收集静态文件（生产环境）"""
        print("📦 收集静态文件...")
        try:
            result = subprocess.run([
                sys.executable, 'manage.py', 'collectstatic', '--noinput'
            ], capture_output=True, text=True, cwd=self.base_dir)
            
            if result.returncode == 0:
                print("✅ 静态文件收集完成")
            else:
                print(f"❌ 静态文件收集失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ 静态文件收集出错: {e}")
            return False
        return True
        
    def show_current_config(self):
        """显示当前配置"""
        if not self.env_file.exists():
            print("❌ 未找到 .env 配置文件")
            return
            
        print("📋 当前环境配置:")
        print("=" * 50)
        
        with open(self.env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    if 'PASSWORD' in line or 'SECRET' in line:
                        # 隐藏敏感信息
                        key = line.split('=', 1)[0]
                        print(f"{key}=***隐藏***")
                    else:
                        print(line)
        print("=" * 50)

def main():
    """主函数"""
    manager = DeploymentManager()
    
    if len(sys.argv) < 2:
        print("Django 项目部署配置工具")
        print("=" * 60)
        print("用法:")
        print("  python deploy.py production [--debug]         - 配置生产环境")
        print("  python deploy.py preproduction                - 配置预生产环境")
        print("  python deploy.py development                  - 配置开发环境")
        print("  python deploy.py status                       - 查看当前配置")
        print("  python deploy.py collectstatic                - 收集静态文件")
        print("")
        print("环境配置说明:")
        print("  🚀 production    - 正式生产环境（从配置文件读取服务器信息）")
        print("  🧪 preproduction - 预生产环境（Git提交前测试）")
        print("  🔧 development   - 开发环境（本地开发调试）")
        print("")
        print("生产环境配置:")
        print("  python deploy.py production                             # 自动读取配置文件")
        print("  python deploy.py production --debug                     # 启用调试模式")
        print("  注意: 服务器IP和域名信息将从 deploy_config.yaml 文件中读取")
        print("")
        print("预生产环境配置:")
        print("  python deploy.py preproduction                          # Git提交前测试")
        print("")
        print("开发环境配置:")
        print("  python deploy.py development                            # 允许内网访问")
        print("")
        print("访问方式说明:")
        print("  - 生产环境: http://example.com:8000 或 http://IP:8000")
        print("  - 预生产环境: http://localhost:8000（仅本地测试）")
        print("  - 开发环境: http://localhost:8000 或内网IP")
        return
    
    command = sys.argv[1].lower()

    # 检查是否有调试参数
    debug_mode = '--debug' in sys.argv

    if command == 'production':
        # 从配置文件自动读取服务器信息
        if manager.create_production_env(debug=debug_mode):
            # 生产环境构建前端资源
            if manager.build_frontend():
                # 收集静态文件
                if manager.collect_static_files():
                    print("\n🎉 生产环境配置完成！")
                    print("请确保:")
                    print("1. 数据库连接正常")
                    print("2. 静态文件目录权限正确")
                    print("3. 域名解析正确（如果使用域名）")
                    print("4. Vue组件已成功构建到templates/vue目录")
            else:
                print("❌ 前端构建失败，生产环境配置中止")
        
    elif command == 'preproduction':
        manager.create_preproduction_env()

        # 预生产环境自动收集静态文件
        if manager.collect_static_files():
            print("\n🧪 预生产环境配置完成！")
            print("⚠️  重要提醒:")
            print("1. 这是预生产环境，用于Git提交前的最终测试")
            print("2. 部署到服务器前，请使用以下命令切换到正式生产环境:")
            print("   python deploy.py production [服务器IP] [域名]")
            print("3. 预生产环境仅允许本地访问，不支持内网访问")

    elif command == 'development':
        manager.create_development_env()
        print("\n🎉 开发环境配置完成！")
        print("现在可以通过内网访问您的应用了")

    elif command == 'status':
        manager.show_current_config()
        
    elif command == 'collectstatic':
        manager.collect_static_files()
        
    else:
        print(f"❌ 未知命令: {command}")
        print("使用 'python deploy.py' 查看帮助")

if __name__ == '__main__':
    main()
